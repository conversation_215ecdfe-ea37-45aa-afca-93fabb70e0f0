:host {
  display: block;
  height: 100%;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
}

.clsc-chat {
  display: flex;
  height: 100%;
  background: #f4f5f7;
}

/* SIDEBAR */

.clsc-chat__sidebar {
  width: 260px;
  background: #020617;
  color: #e5e7eb;
  display: flex;
  flex-direction: column;
  padding: 16px 12px;
}

.clsc-chat__sidebar-header {
  padding: 4px 4px 12px;
}

.clsc-chat__new-btn {
  width: 100%;
  border: none;
  border-radius: 999px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  background: #2563eb;
  cursor: pointer;
}

.clsc-chat__new-btn:hover {
  background: #1d4ed8;
}

.clsc-chat__sidebar-section {
  flex: 1;
  overflow-y: auto;
  padding: 4px;
}

.clsc-chat__sidebar-title {
  font-size: 11px;
  font-weight: 700;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  color: #6b7280;
  margin: 4px 4px 8px;
}

// .clsc-chat__session--active {
//   background: #020617;
//   border: 1px solid #1d4ed8;
// }

.clsc-chat__session:not(.clsc-chat__session--active):hover {
  background: #0f172a;
}

.clsc-chat__session-name {
  font-size: 14px;
  font-weight: 600;
  color: #e5e7eb;
}

.clsc-chat__session-date {
  font-size: 11px;
  color: #9ca3af;
  margin-top: 2px;
}

.clsc-chat__sidebar-footer {
  margin-top: 8px;
  padding: 10px;
  border-radius: 14px;
  background: #020617;
  display: flex;
  align-items: center;
  gap: 8px;
}

.clsc-chat__user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 999px;
  background: linear-gradient(135deg, #6366f1, #ec4899);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
  color: #fff;
}

.clsc-chat__user-meta {
  display: flex;
  flex-direction: column;
}

.clsc-chat__user-name {
  font-size: 14px;
  font-weight: 600;
  color: #f9fafb;
}

.clsc-chat__user-plan {
  font-size: 10px;
  font-weight: 700;
  letter-spacing: 0.12em;
  text-transform: uppercase;
  color: #6b7280;
}

/* header */

.clsc-chat__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;

  background: #ffffff;
  margin: 0;
  padding: 16px 24px 8px;
  border-radius: 0;
  border: none;
  box-shadow: none;
}

.clsc-chat__title {
  font-size: 16px;
  font-weight: 700;
  color: #0f172a;
  margin: 0 0 4px;
}

.clsc-chat__subtitle {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

.clsc-chat__link {
  font-size: 12px;
  color: #2563eb;
  text-decoration: none;
}

.clsc-chat__link:hover {
  text-decoration: underline;
}


.clsc-chip {
  font-size: 12px;
  border-radius: 999px;
  padding: 6px 10px;
  border: 1px solid #e5e7eb;
  background: #f9fafb;
  color: #4b5563;
  cursor: pointer;
}

.clsc-chip--active {
  background: #e0e7ff;
  border-color: #4338ca;
  color: #312e81;
}

.clsc-chip--green.clsc-chip--active {
  background: #dcfce7;
  border-color: #16a34a;
  color: #166534;
}

.clsc-chip--red.clsc-chip--active {
  background: #fee2e2;
  border-color: #dc2626;
  color: #b91c1c;
}

.clsc-chat__toolbar-spacer {
  flex: 1;
}

.clsc-chat__role {
  display: flex;
  align-items: center;
  gap: 6px;
}

.clsc-chat__role-label {
  font-size: 11px;
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: 0.08em;
  color: #6b7280;
}

.clsc-chat__role-select {
  font-size: 12px;
  padding: 6px 8px;
  border-radius: 999px;
  border: 1px solid #e5e7eb;
  background: #f9fafb;
}

/* body */
.clsc-chat__body {
  flex: 1;
  margin-top: 12px;
  overflow-y: auto;
  padding: 0 24px;
}

/* EMPTY STATE */
.clsc-chat__empty {
  max-width: 900px;
  margin: 48px auto 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding-bottom: 80px;
}
.clsc-chat__empty-icon {
  width: 72px;
  height: 72px;
  border-radius: 24px;
  background: linear-gradient(135deg, #2563eb, #4f46e5);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 34px;
  color: #fff;
  margin-bottom: 16px;
}

.clsc-chat__empty-title {
  font-size: 20px;
  font-weight: 700;
  color: #0f172a;
  margin-bottom: 4px;
}

.clsc-chat__empty-desc {
  max-width: 460px;
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 24px;
}
.clsc-chat__suggest-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 20px;
  justify-content: center;
}

/* CARD GỢI Ý */
.clsc-suggest {
  position: relative;
  width: 360px;
  min-height: 96px;
  border-radius: 18px;
  padding: 16px 18px 14px;
  background: #ffffff;
  border: 1px solid #ffe4d6;
  cursor: pointer;
  box-shadow: 0 8px 24px rgba(15, 23, 42, 0.03);

  display: grid;
  grid-template-columns: auto 1fr;
  grid-template-rows: auto auto;
  column-gap: 12px;
  row-gap: 4px;
  text-align: left;
  justify-items: start;
  align-items: flex-start;
}


.clsc-suggest:hover {
  box-shadow: 0 12px 32px rgba(15, 23, 42, 0.08);
  transform: translateY(-1px);
}

.clsc-suggest__icon {
  grid-row: 1 / span 2;
  grid-column: 1;
  width: 32px;
  height: 32px;
  border-radius: 999px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin: 0;
}

.clsc-suggest__text {
  grid-row: 1;
  grid-column: 2;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.clsc-suggest__tag {
  grid-row: 2;
  grid-column: 2;
  align-self: flex-start;
  justify-self: flex-start;

  font-size: 10px;
  padding: 2px 8px;
  border-radius: 999px;
  background: #f3f4f6;
  color: #6b7280;

  display: inline-flex;
  width: auto;
}

/* 1: cam */
.clsc-suggest:nth-child(1) {
  border-color: #fed7aa;
//   background: #fffbeb;
}
.clsc-suggest:nth-child(1) .clsc-suggest__icon {
  background: #f97316;
  color: #fff;
}

/* 2: đỏ */
.clsc-suggest:nth-child(2) {
  border-color: #fecaca;
}
.clsc-suggest:nth-child(2) .clsc-suggest__icon {
  background: #ef4444;
  color: #fff;
}

/* 3: xanh ngọc */
.clsc-suggest:nth-child(3) {
  border-color: #bbf7d0;
}
.clsc-suggest:nth-child(3) .clsc-suggest__icon {
  background: #22c55e;
  color: #fff;
}

/* 4: tím */
.clsc-suggest:nth-child(4) {
  border-color: #e9d5ff;
//   background: #faf5ff;
}
.clsc-suggest:nth-child(4) .clsc-suggest__icon {
  background: #8b5cf6;
  color: #fff;
}

/* messages */

.clsc-chat__messages {
  padding: 12px 0 64px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.clsc-msg {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.clsc-msg--user {
  flex-direction: row-reverse;
}

.clsc-msg__avatar {
  width: 32px;
  height: 32px;
  border-radius: 999px;
  background: #e5f2ff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
  color: #1d4ed8;
}

.clsc-msg--user .clsc-msg__avatar {
  background: #e0f2fe;
  color: #0369a1;
}

.clsc-msg__content {
  max-width: 70%;
}

.clsc-msg__bubble {
  border-radius: 18px;
  padding: 10px 14px;
  font-size: 14px;
  line-height: 1.5;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  color: #111827;
}

.clsc-msg--user .clsc-msg__bubble {
  background: #eff6ff;
  border-color: #bfdbfe;
}

/* sources */

.clsc-msg__sources {
  margin-top: 6px;
}

.clsc-msg__sources-label {
  font-size: 10px;
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: 0.08em;
  color: #9ca3af;
  margin-bottom: 4px;
}

.clsc-msg__sources-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.clsc-source-pill {
  border-radius: 999px;
  border: 1px solid #e5e7eb;
  background: #ffffff;
  padding: 4px 8px;
  font-size: 11px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.clsc-source-pill__dot {
  width: 6px;
  height: 6px;
  border-radius: 999px;
  background: #4f46e5;
}

.clsc-source-pill__page {
  padding: 1px 6px;
  border-radius: 999px;
  background: #f3f4f6;
  font-size: 10px;
}

.clsc-chat__followups {
  width: 100%;
  padding: 8px 24px 6px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.clsc-followup {
  border-radius: 999px;
  border: 1px solid #e5e7eb;
  background: #ffffff;
  padding: 6px 10px;
  font-size: 12px;
  color: #4b5563;
  cursor: pointer;
}

.clsc-followup:hover {
  border-color: #2563eb;
  color: #1d4ed8;
}

/* input */
.clsc-chat__input {
  margin-top: 8px;
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  background: #ffffff;
}

.clsc-chat__input-inner {
  max-width: 960px; 
  margin: 0 auto;
  display: flex;
  align-items: center;
  gap: 8px;

  background: #f9fafb;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  padding: 8px 10px;
  box-shadow: 0 6px 20px rgba(15, 23, 42, 0.06);
}

/* Nút + bên trái – tròn, viền mỏng */
.clsc-input__addon {
  width: 34px;
  height: 34px;
  flex-shrink: 0;

  display: inline-flex;
  align-items: center;
  justify-content: center;

  border-radius: 999px;
  border: 1px solid #d1d5db;
  background: #ffffff;
  color: #6b7280;
  font-size: 18px;
  cursor: pointer;
}

/* Textarea */
.clsc-input__textarea {
  flex: 1;
  border: none;
  background: transparent;
  padding: 8px 6px;
  font-size: 14px;
  resize: none;
  min-height: 32px;
  max-height: 96px;
  line-height: 1.4;
}

.clsc-input__textarea:focus {
  outline: none;
}

/* Nút gửi – tròn, màu xanh */
.clsc-input__send {
  width: 32px;
  height: 32px;
  flex-shrink: 0;

  display: inline-flex;
  align-items: center;
  justify-content: center;

  border-radius: 999px;
  border: none;
  background: #2563eb;
  color: #ffffff;
  font-size: 16px;
  cursor: pointer;
}

.clsc-input__send:disabled {
  opacity: 0.5;
  cursor: default;
}

.commercial-tab--full {
  padding: 0;
}


.clsc-chat {
  display: flex;
  height: 100%;
  background: #f4f5f7;
}

/* SIDEBAR */
.clsc-chat__sidebar {
  width: 260px;
  background: #ffffff;
  color: #111827;
  display: flex;
  flex-direction: column;
  padding: 16px 12px;
  border-right: 1px solid #e5e7eb;
}

.clsc-chat__sidebar-title {
  font-size: 11px;
  font-weight: 700;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  color: #9ca3af;
  margin: 4px 4px 8px;
}

.clsc-chat__session:not(.clsc-chat__session--active):hover {
  background: #f3f4f6;
}

.clsc-chat__session-name {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.clsc-chat__session-date {
  font-size: 11px;
  color: #6b7280;
  margin-top: 2px;
}


.clsc-chat__sidebar {
  width: 260px;
  background: #ffffff;
  color: #111827;
  display: flex;
  flex-direction: column;
  padding: 16px 12px;
  border-right: 1px solid #e5e7eb;
}

.clsc-chat__sidebar-title {
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  color: #9ca3af;
  margin: 4px 4px 8px;
}
/* card lịch sử */
.clsc-chat__session {
  position: relative;
  width: 100%;
  border-radius: 12px;
  border: 1px solid #d1d5db;
  background: #f9fafb;
  padding: 10px 30px 10px 14px;
  text-align: left;
  cursor: pointer;
  margin-bottom: 8px;
  transition: background 0.15s, border-color 0.15s, box-shadow 0.15s;
}

/* hàng chứa text + icon */
.clsc-chat__session-row {
  display: flex;
  align-items: center;
  width: 100%;
}

/* title — nếu dài thì cắt và thêm “…” */
.clsc-chat__session-name {
  font-size: 14px;
  font-weight: 600;
  color: #111827;

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.clsc-chat__session-rename {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);

  border: none;
  background: transparent;
  cursor: pointer;

  width: 18px;
  height: 18px;
  padding: 0;

  display: flex;
  align-items: center;
  justify-content: center;

  font-size: 12px;
  opacity: 0.6;
  outline: none;
}

.clsc-chat__session:hover .clsc-chat__session-rename {
  opacity: 1;
}

.clsc-chat__session-rename i {
  width: 14px;
  height: 14px;
}

.clsc-chat__session-text {
  flex: 1;
  min-width: 0;
  padding-right: 24px;
}

.clsc-chat__session-input {
  width: 100%;
  border: none;
  background: transparent;
  font-size: 14px;
  font-weight: 600;
  outline: none;
  color: #111827;
  padding-right: 0;
  box-sizing: border-box;
}

.clsc-chat__session-input:focus {
  border-radius: 4px;
  background: #ffffff;
}

.clsc-chat__session--active {
  background: #eef2ff;
  border: 1px solid #4f46e5;
}

.clsc-chat__session:not(.clsc-chat__session--active):hover {
  background: #f3f4f6;
}

.clsc-chat__session-name {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.clsc-chat__session-date {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.clsc-chat__top-card {
  background: #ffffff;
  border-radius: 18px;
  padding: 16px 20px 12px;
  box-shadow: 0 12px 32px rgba(15, 23, 42, 0.06);
  border: 1px solid #e5e7eb;
  margin-bottom: 16px;
}


.clsc-chat__title {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
  margin: 0 0 4px;
}

.clsc-chat__subtitle {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.clsc-chat__toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px 12px;
  background: #ffffff; 
}

.clsc-chat {
  display: flex;
  height: 100%;
  background: #f4f5f7;
}

.clsc-chat__main {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  background: transparent;
}

.clsc-chat__main::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  height: 120px;
  background: #ffffff;
  border-bottom: 1px solid #edf2f7;
  z-index: -1;
}

.clsc-chat,
.clsc-chat * {
  font-family: inherit;
}
.clsc-msg__bubble {
  .clsc-msg__markdown {
    font-size: 14px;
    line-height: 1.6;
    white-space: normal;
  }

  .clsc-msg__markdown h1,
  .clsc-msg__markdown h2,
  .clsc-msg__markdown h3 {
    margin: 0 0 6px;
    font-weight: 600;
    font-size: 15px;
  }

  .clsc-msg__markdown p {
    margin: 0 0 6px;
  }

  .clsc-msg__markdown ul,
  .clsc-msg__markdown ol {
    margin: 0 0 6px 18px;
    padding: 0;
  }

  .clsc-msg__markdown li {
    margin-bottom: 2px;
  }

  .clsc-msg__markdown code {
    font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    font-size: 12px;
    padding: 2px 4px;
    border-radius: 4px;
    background: rgba(15, 23, 42, 0.06);
  }
}

.clsc-msg__thinking-inline {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}
.clsc-context {
  position: fixed;            // bám theo viewport
  z-index: 9999;
  min-width: 160px;
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 14px 40px rgba(15, 23, 42, 0.14);
  padding: 6px;
}

.clsc-context__item {
  width: 100%;
  border: none;
  background: transparent;
  text-align: left;
  padding: 10px 10px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 13px;
  color: #111827;
}

.clsc-context__item:hover {
  background: #f3f4f6;
}

.clsc-context__item--danger {
  color: #b91c1c;
}

.clsc-context__item--danger:hover {
  background: #fee2e2;
}

.clsc-modal-backdrop {
  position: fixed;
  inset: 0;
  z-index: 10000;
  background: rgba(15, 23, 42, 0.35);
  backdrop-filter: blur(6px);
  display: grid;
  place-items: center;
  padding: 16px;
}

.clsc-modal {
  width: min(560px, 92vw);
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 24px 80px rgba(15, 23, 42, 0.28);
  padding: 18px 18px 14px;
}

.clsc-modal__title {
  font-size: 16px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 10px;
}

.clsc-modal__desc {
  font-size: 14px;
  color: #4b5563;
  line-height: 1.5;
}

.clsc-modal__actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 18px;
}

.clsc-btn {
  border: none;
  border-radius: 999px;
  padding: 8px 14px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
}

.clsc-btn--ghost {
  background: #ffffff;
  border: 1px solid #d1d5db;
  color: #111827;
}

.clsc-btn--ghost:hover {
  background: #f9fafb;
}

.clsc-btn--danger {
  background: #ef4444;
  color: #ffffff;
}

.clsc-btn--danger:hover {
  background: #dc2626;
}
.clsc-chat__header-left {
  flex: 1;
  min-width: 0; 
}
.clsc-chat__title {
  margin: 0 0 4px;
  font-weight: 700;
  color: #0f172a;

  white-space: normal;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  word-break: break-word;
}

