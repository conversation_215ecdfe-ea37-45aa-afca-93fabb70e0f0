import { Injectable } from '@angular/core';
import {
  GraphFormState,
  ApiNode,
  GraphNode,
  GraphLink,
  DateFilterMode,
} from '../types/graph.types';
import { GraphCacheService } from './graph-cache.service';
import { GraphApiResponse } from '../types/graph.types';

export interface NodeFilterContext {
  hasAuthorityFilter: boolean;
  normalizedSelectedAuthorities: string[];
  hasTypeFilter: boolean;
  normalizedSelectedTypes: string[];
  hasStatusFilter: boolean;
  normalizedSelectedStatuses: string[];
  hasDateFilter: boolean;
  dateFilterMode: DateFilterMode | null | undefined;
  dateFilterFrom: number | null | undefined;
  dateFilterTo: number | null | undefined;
  hasRelTypeFilter: boolean;
  selectedRelTypes: string[];
  nodesReachableViaRelType: Set<string> | null;
  seedNodeIds: Set<string>; // All seed node IDs
  // Nodes that pass both attribute filters AND relationship filter (AND logic)
  nodesPassingBothFilters: Set<string> | null;
  // Seed nodes that connect to nodes passing both filters
  seedNodesConnectedToFilteredNodes: Set<string> | null;
}

export interface NodeVisualState {
  isBlurred: boolean;
  opacity: number;
}

/**
 * Service for filtering graph nodes and links
 */
@Injectable({
  providedIn: 'root',
})
export class GraphFilterService {
  constructor(private cacheService: GraphCacheService) {}

  /**
   * Build filter context from form state
   */
  buildNodeFilterContext(
    formState: GraphFormState | null,
    seedNodeIds: Set<string>,
    links: GraphLink[],
    nodes?: GraphNode[],
    apiNodeMap?: Map<string, ApiNode>,
    findApiNodeById?: (id: string) => ApiNode | null
  ): NodeFilterContext {
    const selectedAuthorities = formState?.selectedCoQuanBanHanh || [];
    const normalizedSelectedAuthorities = selectedAuthorities.map((a) =>
      (a || '').trim()
    );

    const selectedTypes = formState?.selectedBoLocLoaiVanBan || [];
    const normalizedSelectedTypes = selectedTypes.map((t) => (t || '').trim());

    const selectedStatuses = formState?.selectedTinhTrangHieuLuc || [];
    const normalizedSelectedStatuses = selectedStatuses.map((s) =>
      (s || '').trim()
    );

    const selectedRelTypes = formState?.selectedBoLocMoiQuanHe || [];

    const dateFilterMode = formState?.dateFilterMode;
    const dateFilterFrom = formState?.dateFilterFrom;
    const dateFilterTo = formState?.dateFilterTo;

    const hasAuthorityFilter = normalizedSelectedAuthorities.length > 0;
    const hasTypeFilter = normalizedSelectedTypes.length > 0;
    const hasStatusFilter = normalizedSelectedStatuses.length > 0;
    const hasRelTypeFilter = selectedRelTypes.length > 0;
    const hasDateFilter =
      dateFilterMode !== null &&
      dateFilterMode !== undefined &&
      dateFilterFrom !== null &&
      dateFilterTo !== null;

    let nodesReachableViaRelType: Set<string> | null = null;
    if (hasRelTypeFilter && seedNodeIds.size > 0) {
      const reachableSets = selectedRelTypes.map((relType) =>
        this.getReachableNodesForRelationshipType(relType, seedNodeIds, links)
      );

      if (reachableSets.length > 0) {
        // OR logic: include nodes reachable via ANY selected relationship type
        // Note: seed nodes are already included in each set from getReachableNodesForRelationshipType
        nodesReachableViaRelType = new Set<string>();
        reachableSets.forEach((set) => {
          set.forEach((nodeId) => nodesReachableViaRelType!.add(nodeId));
        });
      }
    }

    // Build combined filter sets when both attribute filters and relationship filter are active
    let nodesPassingBothFilters: Set<string> | null = null;
    let seedNodesConnectedToFilteredNodes: Set<string> | null = null;
    
    const hasAnyAttributeFilter = hasAuthorityFilter || hasTypeFilter || hasStatusFilter || hasDateFilter;
    
    if (hasAnyAttributeFilter && hasRelTypeFilter && nodes && apiNodeMap && findApiNodeById) {
      // Find nodes that pass attribute filters AND connect to seed nodes via selected relationship types
      nodesPassingBothFilters = new Set<string>();
      seedNodesConnectedToFilteredNodes = new Set<string>();
      
      // First, find all nodes that pass attribute filters
      const nodesPassingAttributeFilters = new Set<string>();
      nodes.forEach((node) => {
        const apiNode = apiNodeMap.get(node.id) ?? findApiNodeById(node.id);
        if (!apiNode) return;
        
        let passesFilters = true;
        
        if (hasAuthorityFilter) {
          const coQuanBanHanh = (apiNode.thuoc_tinh?.co_quan_ban_hanh || '').trim();
          if (!this.matchesAllSelections(coQuanBanHanh, normalizedSelectedAuthorities)) {
            passesFilters = false;
          }
        }
        
        if (hasTypeFilter && passesFilters) {
          const loaiVanBan = (apiNode.thuoc_tinh?.loai_van_ban || '').trim();
          if (!this.matchesAllSelections(loaiVanBan, normalizedSelectedTypes)) {
            passesFilters = false;
          }
        }
        
        if (hasStatusFilter && passesFilters) {
          const tinhTrangHieuLuc = (apiNode.thuoc_tinh?.tinh_trang_hieu_luc || '').trim();
          if (!this.matchesAllSelections(tinhTrangHieuLuc, normalizedSelectedStatuses)) {
            passesFilters = false;
          }
        }
        
        if (hasDateFilter && passesFilters && dateFilterFrom !== null && dateFilterTo !== null) {
          const dateString = dateFilterMode === 'ban_hanh'
            ? apiNode.thuoc_tinh?.ngay_ban_hanh || ''
            : apiNode.thuoc_tinh?.ngay_co_hieu_luc || '';
          if (!this.isDateInRange(dateString, dateFilterFrom, dateFilterTo)) {
            passesFilters = false;
          }
        }
        
        if (passesFilters) {
          nodesPassingAttributeFilters.add(node.id);
        }
      });
      
      // Then, find nodes that pass attribute filters AND connect to seed nodes via selected relationship types
      // Also track which seed nodes are connected to these filtered nodes
      nodesPassingAttributeFilters.forEach((nodeId) => {
        // Check if this node connects to any seed node via selected relationship types
        const connectsToSeed = links.some((link) => {
          const sourceId = String(link.source);
          const targetId = String(link.target);
          const relType = link.__relationshipType;
          
          // Check if link has selected relationship type
          if (!relType || !selectedRelTypes.includes(relType)) {
            return false;
          }
          
          // Check if link connects this node to a seed node (bidirectional)
          const nodeIsSource = sourceId === nodeId && seedNodeIds.has(targetId);
          const nodeIsTarget = targetId === nodeId && seedNodeIds.has(sourceId);
          
          if (nodeIsSource || nodeIsTarget) {
            // Track the connected seed node
            if (nodeIsSource) seedNodesConnectedToFilteredNodes!.add(targetId);
            if (nodeIsTarget) seedNodesConnectedToFilteredNodes!.add(sourceId);
            return true;
          }
          
          return false;
        });
        
        if (connectsToSeed) {
          nodesPassingBothFilters.add(nodeId);
        }
      });
      
      // If no nodes pass both filters, set to empty set (not null) to indicate "blur everything"
      if (nodesPassingBothFilters.size === 0) {
        nodesPassingBothFilters = new Set<string>();
        seedNodesConnectedToFilteredNodes = new Set<string>();
      }
    }

    return {
      hasAuthorityFilter,
      normalizedSelectedAuthorities,
      hasTypeFilter,
      normalizedSelectedTypes,
      hasStatusFilter,
      normalizedSelectedStatuses,
      hasDateFilter,
      dateFilterMode,
      dateFilterFrom,
      dateFilterTo,
      hasRelTypeFilter,
      selectedRelTypes,
      nodesReachableViaRelType,
      seedNodeIds,
      nodesPassingBothFilters,
      seedNodesConnectedToFilteredNodes,
    };
  }

  /**
   * Get visual state for a node based on filters
   */
  getNodeVisualState(
    nodeId: string,
    apiNode: ApiNode | null,
    context: NodeFilterContext,
    rootNodeId: string,
    findApiNodeById: (id: string) => ApiNode | null
  ): NodeVisualState {
    const {
      hasAuthorityFilter,
      normalizedSelectedAuthorities,
      hasTypeFilter,
      normalizedSelectedTypes,
      hasStatusFilter,
      normalizedSelectedStatuses,
      hasDateFilter,
      dateFilterMode,
      dateFilterFrom,
      dateFilterTo,
      hasRelTypeFilter,
      nodesReachableViaRelType,
      nodesPassingBothFilters,
      seedNodesConnectedToFilteredNodes,
    } = context;

    const hasAnyAttributeFilter = hasAuthorityFilter || hasTypeFilter || hasStatusFilter || hasDateFilter;
    const isSeedNode = context.seedNodeIds.has(nodeId);

    // When both attribute filters AND relationship filter are active: use AND logic
    if (hasAnyAttributeFilter && hasRelTypeFilter) {
      // Check if this node passes both filters
      if (nodesPassingBothFilters) {
        // If node is in the combined set, highlight it
        if (nodesPassingBothFilters.has(nodeId)) {
          return { isBlurred: false, opacity: 1 };
        }
        // If it's a seed node connected to filtered nodes, highlight it
        if (isSeedNode && seedNodesConnectedToFilteredNodes && seedNodesConnectedToFilteredNodes.has(nodeId)) {
          return { isBlurred: false, opacity: 1 };
        }
        // Otherwise, blur it (including seed nodes that don't connect to filtered nodes)
        return { isBlurred: true, opacity: 0.1 };
      }
      // If no nodes pass both filters, blur everything
      return { isBlurred: true, opacity: 0.1 };
    }

    // Only attribute filters active (no relationship filter)
    if (hasAnyAttributeFilter && !hasRelTypeFilter) {
      const nodeData = apiNode ?? findApiNodeById(nodeId);
      let isBlurred = false;
      let nodeOpacity = 1;

      // Seed nodes are NOT exempt - treat all nodes the same
      if (hasAuthorityFilter) {
        const coQuanBanHanh = (nodeData?.thuoc_tinh?.co_quan_ban_hanh || '').trim();
        if (!this.matchesAllSelections(coQuanBanHanh, normalizedSelectedAuthorities)) {
          isBlurred = true;
          nodeOpacity = 0.1;
        }
      }

      if (hasTypeFilter && !isBlurred) {
        const loaiVanBan = (nodeData?.thuoc_tinh?.loai_van_ban || '').trim();
        if (!this.matchesAllSelections(loaiVanBan, normalizedSelectedTypes)) {
          isBlurred = true;
          nodeOpacity = 0.1;
        }
      }

      if (hasStatusFilter && !isBlurred) {
        const tinhTrangHieuLuc = (nodeData?.thuoc_tinh?.tinh_trang_hieu_luc || '').trim();
        if (!this.matchesAllSelections(tinhTrangHieuLuc, normalizedSelectedStatuses)) {
          isBlurred = true;
          nodeOpacity = 0.1;
        }
      }

      if (hasDateFilter && !isBlurred && dateFilterFrom !== null && dateFilterTo !== null) {
        const dateString = dateFilterMode === 'ban_hanh'
          ? nodeData?.thuoc_tinh?.ngay_ban_hanh || ''
          : nodeData?.thuoc_tinh?.ngay_co_hieu_luc || '';
        if (!this.isDateInRange(dateString, dateFilterFrom, dateFilterTo)) {
          isBlurred = true;
          nodeOpacity = 0.1;
        }
      }

      return { isBlurred, opacity: nodeOpacity };
    }

    // Only relationship filter active (no attribute filters)
    if (!hasAnyAttributeFilter && hasRelTypeFilter) {
      // Seed nodes are always highlighted for relationship filter
      if (isSeedNode) {
        return { isBlurred: false, opacity: 1 };
      }
      // Non-seed nodes: check if reachable via selected relationship types
      if (nodesReachableViaRelType && nodesReachableViaRelType.has(nodeId)) {
        return { isBlurred: false, opacity: 1 };
      }
      return { isBlurred: true, opacity: 0.1 };
    }

    // No filters active - highlight everything
    return { isBlurred: false, opacity: 1 };
  }

  /**
   * Build set of active node IDs that pass all filters
   */
  buildActiveNodeIdsForFilters(
    nodes: GraphNode[],
    apiNodeMap: Map<string, ApiNode>,
    context: NodeFilterContext,
    rootNodeId: string,
    findApiNodeById: (id: string) => ApiNode | null
  ): Set<string> {
    const {
      hasAuthorityFilter,
      normalizedSelectedAuthorities,
      hasTypeFilter,
      normalizedSelectedTypes,
      hasStatusFilter,
      normalizedSelectedStatuses,
      hasDateFilter,
      dateFilterMode,
      dateFilterFrom,
      dateFilterTo,
    } = context;

    const activeNodeIds = new Set<string>();

    const requiresFiltering =
      hasAuthorityFilter || hasTypeFilter || hasStatusFilter || hasDateFilter;

    if (requiresFiltering) {
      nodes.forEach((node) => {
        const apiNode = apiNodeMap.get(node.id) ?? findApiNodeById(node.id);
        let nodeMatches = true;

        if (!apiNode) {
          nodeMatches = false;
        } else {
          if (hasAuthorityFilter) {
            const coQuanBanHanh =
              (apiNode.thuoc_tinh?.co_quan_ban_hanh || '').trim();
            if (
              !this.matchesAllSelections(
                coQuanBanHanh,
                normalizedSelectedAuthorities
              )
            ) {
              nodeMatches = false;
            }
          }

          if (hasTypeFilter && nodeMatches) {
            const loaiVanBan = (apiNode.thuoc_tinh?.loai_van_ban || '').trim();
            if (
              !this.matchesAllSelections(loaiVanBan, normalizedSelectedTypes)
            ) {
              nodeMatches = false;
            }
          }

          if (hasStatusFilter && nodeMatches) {
            const tinhTrangHieuLuc = (
              apiNode.thuoc_tinh?.tinh_trang_hieu_luc || ''
            ).trim();
            if (
              !this.matchesAllSelections(
                tinhTrangHieuLuc,
                normalizedSelectedStatuses
              )
            ) {
              nodeMatches = false;
            }
          }

          if (
            hasDateFilter &&
            nodeMatches &&
            dateFilterFrom !== null &&
            dateFilterTo !== null
          ) {
            const dateString =
              dateFilterMode === 'ban_hanh'
                ? apiNode.thuoc_tinh?.ngay_ban_hanh || ''
                : apiNode.thuoc_tinh?.ngay_co_hieu_luc || '';
            if (!this.isDateInRange(dateString, dateFilterFrom, dateFilterTo)) {
              nodeMatches = false;
            }
          }
        }

        if (nodeMatches) {
          activeNodeIds.add(node.id);
        }
      });
    }

    // Always include seed nodes
    context.seedNodeIds.forEach((seedId) => activeNodeIds.add(seedId));

    return activeNodeIds;
  }

  /**
   * Determine if a link should be highlighted based on filters
   */
  shouldHighlightLink(
    sourceId: string,
    targetId: string,
    relationshipType: string | undefined,
    context: NodeFilterContext,
    activeNodeIds: Set<string>
  ): boolean {
    const {
      hasRelTypeFilter,
      selectedRelTypes,
      hasAuthorityFilter,
      hasTypeFilter,
      hasStatusFilter,
      hasDateFilter,
      nodesReachableViaRelType,
      nodesPassingBothFilters,
      seedNodesConnectedToFilteredNodes,
    } = context;

    const hasAnyAttributeFilter = hasAuthorityFilter || hasTypeFilter || hasStatusFilter || hasDateFilter;

    // When both attribute filters AND relationship filter are active: highlight links between filtered nodes
    if (hasAnyAttributeFilter && hasRelTypeFilter) {
      if (!nodesPassingBothFilters || nodesPassingBothFilters.size === 0) {
        // No nodes pass both filters - blur all links
        return false;
      }
      
      // Check if link has selected relationship type
      if (!relationshipType || !selectedRelTypes.includes(relationshipType)) {
        return false;
      }
      
      // Check if both source and target are in the filtered set (or seed nodes connected to filtered nodes)
      const sourceIsFiltered = nodesPassingBothFilters.has(sourceId) ||
        (seedNodesConnectedToFilteredNodes && seedNodesConnectedToFilteredNodes.has(sourceId));
      const targetIsFiltered = nodesPassingBothFilters.has(targetId) ||
        (seedNodesConnectedToFilteredNodes && seedNodesConnectedToFilteredNodes.has(targetId));
      
      return sourceIsFiltered && targetIsFiltered;
    }

    // Only attribute filters active (no relationship filter) - don't highlight links
    if (hasAnyAttributeFilter && !hasRelTypeFilter) {
      return false;
    }

    // Only relationship filter active (no attribute filters) - highlight links based on relationship type
    if (!hasAnyAttributeFilter && hasRelTypeFilter) {
      if (!relationshipType || !selectedRelTypes.includes(relationshipType)) {
        return false;
      }
      
      const reachable = nodesReachableViaRelType;
      const sourceIsReachable = reachable?.has(sourceId);
      const targetIsReachable = reachable?.has(targetId);
      
      return !!sourceIsReachable && !!targetIsReachable;
    }

    // No filters active - highlight all links
    return true;
  }

  /**
   * Get directly connected nodes (1-hop) for a specific relationship type from all seed nodes
   * Only finds nodes that are directly connected to any seed node via the relationship type
   * Bidirectional: considers both incoming and outgoing connections
   */
  private getReachableNodesForRelationshipType(
    relType: string,
    seedNodeIds: Set<string>,
    links: GraphLink[]
  ): Set<string> {
    const reachable = new Set<string>();
    if (seedNodeIds.size === 0) {
      return reachable;
    }

    // Always include all seed nodes
    seedNodeIds.forEach((seedId) => reachable.add(seedId));

    // Find all nodes directly connected (1-hop) to any seed node via this relationship type
    links.forEach((link) => {
      if (link.__relationshipType === relType) {
        const sourceId = String(link.source);
        const targetId = String(link.target);
        
        // Check if source is a seed node -> target is directly connected
        if (seedNodeIds.has(sourceId)) {
          reachable.add(targetId);
        }
        
        // Check if target is a seed node -> source is directly connected (bidirectional)
        if (seedNodeIds.has(targetId)) {
          reachable.add(sourceId);
        }
      }
    });

    return reachable;
  }

  /**
   * Check if a value matches ANY selection (OR logic)
   */
  private matchesAllSelections(
    value: string,
    normalizedSelections: string[]
  ): boolean {
    if (!normalizedSelections.length) {
      return true;
    }
    const normalizedValue = (value || '').trim();
    return normalizedSelections.some((selected) => selected === normalizedValue);
  }

  /**
   * Check if a date string falls within the specified year range
   */
  isDateInRange(dateString: string, fromTimestamp: number, toTimestamp: number): boolean {
    if (!dateString) return false;

    try {
      const date = new Date(dateString);
      const time = date.getTime();
      if (isNaN(time)) return false;

      return time >= fromTimestamp && time <= toTimestamp;
    } catch {
      return false;
    }
  }
}

