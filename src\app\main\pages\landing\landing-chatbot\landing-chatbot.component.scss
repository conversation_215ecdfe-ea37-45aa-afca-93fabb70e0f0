/* ========== Chatbot base ========== */
:host {
  display: block;
  width: 100%;
}

:root {
  --cb-min: 520px;
  --cb-fluid: 68vw;
  --cb-max: 920px;
}

/* Container chính */
.chatbot {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  width: clamp(var(--cb-min), var(--cb-fluid), var(--cb-max));
  max-width: none;
  margin: 0 auto;
  // padding: clamp(80px, 12vh, 120px) 0 clamp(72px, 11vh, 120px);
  padding: clamp(32px, 5vh, 48px) 0 clamp(24px, 4vh, 36px);
}

.chatbot__hero {
  text-align: center;
}

.chatbot__title {
  display: inline-block;
  white-space: nowrap;
  transform: translateX(-50%);
  position: relative;
  left: 50%;

  font-size: clamp(26px, 3vw, 34px);
  font-weight: 700;
  line-height: 1.35;
  margin-bottom: 32px;

  background: linear-gradient(90deg, #2D7DD2, #C6C3D4, #F7C824);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  color: transparent;
}
.chatbot__title br + * {
  white-space: normal;
}


.chatbot__title br + * {
  white-space: normal;
}


@keyframes cb-title-wave {
  0%   { background-position:   0% 0; }
  50%  { background-position: 100% 0; }
  100% { background-position:   0% 0; }
}
.chatbot__search {
  position: relative;
  width: 900px;
  max-width: 92%;
  margin: 0 auto 26px;
  padding: 12px;
  border-radius: 32px;
  background: #ffffff;
  box-shadow: 0 8px 18px rgba(0, 0, 0, 0.03);
}

.chatbot__search::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 2px;
  pointer-events: none;

  background: linear-gradient(90deg, #2D8DFE, #B8B6C8, #F7D44A);
  -webkit-mask:
    linear-gradient(#000 0 0) content-box,
    linear-gradient(#000 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

.chatbot__input {
  width: 100%;
  height: 110px;
  padding: 16px 70px 16px 20px;
  border-radius: 24px;
  border: none;
  background: transparent;
  font-size: 16px;
  line-height: 22px;
  resize: none;
  outline: none;
  box-shadow: none;
}

@keyframes cb-border-soft {
  0%   { background-position:   0% 0; }
  50%  { background-position: 100% 0; }
  100% { background-position:   0% 0; }
}

.chatbot__input:focus {
  border-color: #b8c8ff;
}


.chatbot__input::placeholder {
  color: #A3A9B3;
}
.chatbot__send {
  position: absolute;
  right: 18px;
  bottom: 20px;

  width: 34px;
  height: 34px;

  border-radius: 50%;
  border: 1px solid #d1d5db;

  background: #ffffff;
  color: #9ca3af;

  display: grid;
  place-items: center;

  cursor: pointer;
  transition: 0.15s ease;
}

.chatbot__send:hover {
  background: #f3f4f6;
}


.chatbot__send:hover:not(:disabled) {
  background: #E5E7EB;
  color: #111827;
  box-shadow: 0 4px 10px rgba(15, 23, 42, .15);
}

.chatbot__send:active:not(:disabled) {
  transform: translateY(1px);
}

.chatbot__send:disabled {
  opacity: .5;
  cursor: default;
  box-shadow: none;
}


.chatbot__send:hover:not(:disabled) {
  background: #E5E7EB;
  color: #111827;
  box-shadow: 0 4px 10px rgba(15, 23, 42, .15);
}

.chatbot__send:active:not(:disabled) {
  transform: translateY(1px);
}

.chatbot__send:disabled {
  opacity: .5;
  cursor: default;
  box-shadow: none;
}

/* ===== Chips gợi ý ===== */
.chatbot__chips {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-top: 6px;
}

.chip {
  padding: 8px 16px;
  border-radius: 999px;
  border: 1px solid rgba(148, 163, 184, .4);
  background: #ffffff;
  font-size: 13px;
  color: #111827;
  cursor: pointer;
  user-select: none;
  transition:
    background-color .15s ease,
    color .15s ease,
    box-shadow .15s ease,
    transform .1s ease;
}

.chip:hover {
  background: #F3F4FF;
  box-shadow: 0 2px 6px rgba(15, 23, 42, .08);
  transform: translateY(-1px);
}

/* ===== Responsive ===== */

@media (min-width: 1200px) {
  .chatbot {
    width: clamp(560px, 60vw, 980px);
  }
}

@media (max-width: 992px) {
  :root {
    --cb-min: 460px;
    --cb-fluid: 84vw;
    --cb-max: 760px;
  }
}

@media (max-width: 768px) {
  :root {
    --cb-min: 320px;
    --cb-fluid: 94vw;
    --cb-max: 680px;
  }

  .chatbot__title {
    font-size: 20px;
    margin-bottom: 18px;
  }

  .chatbot__search {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .chatbot {
    padding-top: 32px;
  }

  .chatbot__title {
    font-size: 18px;
    line-height: 1.35;
  }

  .chatbot__input {
    padding: 10px 14px;
    font-size: 14px;
  }

  .chatbot__send {
    width: 32px;
    height: 32px;
    margin-right: 6px;
  }
}

@media (max-width: 480px) {
  .chatbot__title {
    font-size: 17px;
  }
}
.chatbot__placeholder {
  position: absolute;

  left: 34px;
  right: 70px; 

  top: 28px; 

  font-size: 16px;
  line-height: 22px;
  color: #A3A9B3;

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  pointer-events: none;
  text-align: left;
  opacity: 1;
  transform: translateY(0);
  transition: opacity .35s ease, transform .35s ease;
}

.chatbot__placeholder.is-fading-out {
  opacity: 0;
  transform: translateY(-4px);
}

