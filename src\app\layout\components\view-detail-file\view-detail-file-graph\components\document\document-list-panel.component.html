<div
  class="document-list-container"
  [ngClass]="{ expanded: expanded }"
>
  <div class="document-list-panel">
    <div
      class="document-list-header d-flex align-items-center justify-content-between"
    >
      <ul
        ngbNav
        #navDocumentList="ngbNav"
        class="nav-tabs"
        [(activeId)]="activeDocumentTab"
        (activeIdChange)="onActiveTabChange($event)"
      >
        <li ngbNavItem="timkiem">
          <a ngbNavLink class="font-weight-bolder">Tài liệu</a>
          <ng-template ngbNavContent>
            <div class="document-list-table-wrapper">
              <div class="document-list-search-container">
                <div class="input-group input-group-merge">
                  <div class="input-group-prepend">
                    <span class="input-group-text">
                      <i data-feather="search" class="text-muted"></i>
                    </span>
                  </div>
                  <input
                    type="text"
                    class="form-control"
                    id="document-search"
                    placeholder="Tìm kiếm"
                    aria-label="Tìm kiếm"
                    aria-describedby="document-search"
                    [(ngModel)]="documentListSearch"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="onSearchChange($event)"
                  />
                </div>
              </div>
              <table class="document-list-table">
                <thead>
                  <tr
                    *ngIf="getFilteredDocumentList().length > 0; else noDocumentSearchResult"
                  >
                    <th
                      colspan="2"
                      class="d-flex align-items-center justify-content-between"
                    >
                      <div class="custom-control custom-checkbox">
                        <input
                          type="checkbox"
                          class="custom-control-input"
                          id="selectAllDocuments"
                          name="selectAllDocuments"
                          [(ngModel)]="selectAllDocuments"
                          (change)="onSelectAllChange($event.target.checked)"
                        />
                        <label class="custom-control-label" for="selectAllDocuments">
                          Chọn tất cả
                        </label>
                      </div>
                      <div
                        (click)="onToggleFullTable()"
                        class="cursor-pointer show-button-toggle d-flex align-items-center"
                      >
                        <img
                          src="assets/images/icons/show-table.svg"
                          alt="show-table"
                        />
                        <span class="show-button-label ml-1">Dạng bảng</span>
                      </div>
                    </th>
                  </tr>
                  <ng-template #noDocumentSearchResult>
                    <tr>
                      <th colspan="2" class="text-center text-muted py-1">
                        Không có kết quả tìm kiếm
                      </th>
                    </tr>
                  </ng-template>
                </thead>
                <tbody>
                  <tr
                    *ngFor="let item of getFilteredDocumentList(); let i = index"
                    [ngClass]="{
                      'document-row--highlighted': item.id === highlightedDocumentId
                    }"
                    [attr.data-document-row]="item.id"
                    (mouseenter)="onDocumentHover(item.id)"
                    (mouseleave)="onDocumentHoverLeave()"
                  >
                    <td class="d-flex align-items-center">
                      <div class="custom-control custom-checkbox">
                        <input
                          type="checkbox"
                          class="custom-control-input mr-1"
                          [id]="'document-' + item.id"
                          [name]="'document-' + item.id"
                          [(ngModel)]="item.selected"
                          (change)="onDocumentSelectChange(item, $event.target.checked)"
                        />
                        <label
                          class="custom-control-label"
                          [for]="'document-' + item.id"
                        >
                          {{ item.title }}
                        </label>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </ng-template>
        </li>

        <li class="locnhan-container" ngbNavItem="locnhanh">
          <a ngbNavLink class="font-weight-bolder">Lọc điều kiện</a>
          <ng-template ngbNavContent>
            <div>
              <ngb-accordion #accQuickFilter="ngbAccordion">
                <!-- Chế độ xem -->
                <ngb-panel id="panel-chedoxem">
                  <ng-template ngbPanelTitle>
                    <span class="d-flex align-items-center justify-content-between w-100">
                      Chế độ xem
                      <i class="feather" [ngClass]="accQuickFilter.isExpanded('panel-chedoxem') ? 'icon-chevron-down' : 'icon-chevron-right'"></i>
                    </span>
                  </ng-template>
                  <ng-template ngbPanelContent>
                    <div>
                      <div class="btn-group btn-group-toggle d-flex" ngbRadioGroup name="sidebarRadioBasic" [(ngModel)]="formState.search_legal_term" (ngModelChange)="onViewModeChange($event)">
                        <label ngbButtonLabel class="btn-outline-primary-theme btn-sm flex-fill" rippleEffect>
                          <input ngbButton type="radio" value="VAN_BAN" /> Văn bản
                        </label>
                        <label ngbButtonLabel class="btn-outline-primary-theme btn-sm flex-fill" rippleEffect>
                          <input ngbButton type="radio" value="DIEU_KHOAN" /> Điều khoản
                        </label>
                        <label ngbButtonLabel class="btn-outline-primary-theme btn-sm flex-fill" rippleEffect>
                          <input ngbButton type="radio" value="ALL" /> Văn bản & Điều khoản
                        </label>
                      </div>
                    </div>
                  </ng-template>
                </ngb-panel>

                <!-- Mối quan hệ với tài liệu gốc -->
                <ngb-panel id="panel-moiquanhe">
                  <ng-template ngbPanelTitle>
                    <span class="d-flex align-items-center justify-content-between w-100">
                      Mối quan hệ với tài liệu gốc
                      <i class="feather" [ngClass]="accQuickFilter.isExpanded('panel-moiquanhe') ? 'icon-chevron-down' : 'icon-chevron-right'"></i>
                    </span>
                  </ng-template>
                  <ng-template ngbPanelContent>
                    <app-search-checkbox-list
                      [options]="boLocMoiQuanHeOptions"
                      [selectedValues]="formState.selectedBoLocMoiQuanHe"
                      [idPrefix]="'moiquanhe'"
                      [searchPlaceholder]="'Tìm kiếm'"
                      (selectionChange)="onMoiQuanHeChange($event)"
                    ></app-search-checkbox-list>
                  </ng-template>
                </ngb-panel>

                <!-- Cơ quan ban hành -->
                <ngb-panel id="panel-coquanbanhanh">
                  <ng-template ngbPanelTitle>
                    <span class="d-flex align-items-center justify-content-between w-100">
                      Cơ quan ban hành
                      <i class="feather" [ngClass]="accQuickFilter.isExpanded('panel-coquanbanhanh') ? 'icon-chevron-down' : 'icon-chevron-right'"></i>
                    </span>
                  </ng-template>
                  <ng-template ngbPanelContent>
                    <app-search-checkbox-list
                      [options]="coQuanBanHanhOptions"
                      [selectedValues]="formState.selectedCoQuanBanHanh"
                      [idPrefix]="'coquanbanhanh'"
                      [searchPlaceholder]="'Tìm kiếm'"
                      (selectionChange)="onCoQuanBanHanhChange($event)"
                    ></app-search-checkbox-list>
                  </ng-template>
                </ngb-panel>

                <!-- Loại văn bản -->
                <ngb-panel id="panel-loaivanban">
                  <ng-template ngbPanelTitle>
                    <span class="d-flex align-items-center justify-content-between w-100">
                      Loại văn bản
                      <i class="feather" [ngClass]="accQuickFilter.isExpanded('panel-loaivanban') ? 'icon-chevron-down' : 'icon-chevron-right'"></i>
                    </span>
                  </ng-template>
                  <ng-template ngbPanelContent>
                    <app-search-checkbox-list
                      [options]="boLocLoaiVanBanOptions"
                      [selectedValues]="formState.selectedBoLocLoaiVanBan"
                      [idPrefix]="'loaivanban'"
                      [searchPlaceholder]="'Tìm kiếm'"
                      [colorMap]="loaiVanBanColorMap"
                      (selectionChange)="onLoaiVanBanChange($event)"
                    ></app-search-checkbox-list>
                  </ng-template>
                </ngb-panel>

                <!-- Trạng thái hiệu lực -->
                <ngb-panel id="panel-trangthaihieuluc">
                  <ng-template ngbPanelTitle>
                    <span class="d-flex align-items-center justify-content-between w-100">
                      Trạng thái hiệu lực
                      <i class="feather" [ngClass]="accQuickFilter.isExpanded('panel-trangthaihieuluc') ? 'icon-chevron-down' : 'icon-chevron-right'"></i>
                    </span>
                  </ng-template>
                  <ng-template ngbPanelContent>
                    <app-search-checkbox-list
                      [options]="tinhTrangHieuLucOptions"
                      [selectedValues]="formState.selectedTinhTrangHieuLuc"
                      [idPrefix]="'trangthaihieuluc'"
                      [searchPlaceholder]="'Tìm kiếm'"
                      (selectionChange)="onTrangThaiHieuLucChange($event)"
                    ></app-search-checkbox-list>
                  </ng-template>
                </ngb-panel>

                <!-- Lọc theo thời gian -->
                <ngb-panel id="panel-loctheothoigian">
                  <ng-template ngbPanelTitle>
                    <span class="d-flex align-items-center justify-content-between w-100">
                      Lọc theo thời gian
                      <i class="feather" [ngClass]="accQuickFilter.isExpanded('panel-loctheothoigian') ? 'icon-chevron-down' : 'icon-chevron-right'"></i>
                    </span>
                  </ng-template>
                  <ng-template ngbPanelContent>
                    <div class="date-filter-content">
                      <!-- Radio options for date filter -->
                      <div class="mb-1">
                        <div class="form-check form-check-inline mb-50">
                          <input
                            type="radio"
                            class="form-check-input"
                            id="date-filter-ban-hanh"
                            name="date-filter-mode"
                            [checked]="formState?.dateFilterMode === 'ban_hanh'"
                            (click)="onDateFilterModeChange('ban_hanh', $event)"
                          />
                          <label class="form-check-label graph-date-label" for="date-filter-ban-hanh">
                            Lọc theo thời gian ban hành
                          </label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input
                            type="radio"
                            class="form-check-input"
                            id="date-filter-hieu-luc"
                            name="date-filter-mode"
                            [checked]="formState?.dateFilterMode === 'hieu_luc'"
                            (click)="onDateFilterModeChange('hieu_luc', $event)"
                          />
                          <label class="form-check-label graph-date-label" for="date-filter-hieu-luc">
                            Lọc theo thời gian có hiệu lực
                          </label>
                        </div>
                      </div>

                      <!-- Sample noUI slider with fake data -->
                      <div class="form-group mt-1 mb-1" *ngIf="formState?.dateFilterMode">
                        <div class="sample-year-slider mb-1">
                          <div class="d-flex align-items-center justify-content-between mb-25">
                            <span class="font-weight-bold">
                              {{ formState?.dateFilterMode === 'ban_hanh' ? 'Ban hành' : 'Hiệu lực' }}
                            </span>
                            <span class="year-slider-range">
                              {{ yearSliderValue[0] }} - {{ yearSliderValue[1] }}
                            </span>
                          </div>

                          <div class="year-histogram mb-25">
                            <div
                              class="year-bar"
                              *ngFor="let bucket of normalizedYearBuckets"
                              [style.height.%]="bucket.height"
                              [attr.title]="bucket.year + ': ' + bucket.value"
                            ></div>
                          </div>

                          <nouislider
                            #yearSlider
                            [config]="yearSliderConfig"
                            [(ngModel)]="yearSliderValue"
                            (ngModelChange)="onYearSliderChange($event)"
                            (change)="onYearSliderFinal($event)"
                          ></nouislider>

                          <div class="year-slider-scale d-flex justify-content-between mt-50">
                            <span>{{ yearBuckets[0]?.year || 1990 }}</span>
                            <span>{{ yearBuckets[yearBuckets.length - 1]?.year || 2025 }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </ngb-panel>
                
              </ngb-accordion>
            </div>
          </ng-template>
        </li>
      </ul>

      <img
        class="cursor-pointer toggle-icon"
        *ngIf="expanded"
        src="assets/images/icons/expand.svg"
        (click)="onToggle()"
        [ngbTooltip]="'Thu gọn danh sách'"
        container="body"
        aria-label="Đóng"
      />
    </div>
    <div class="nav-documentlist-container" [ngbNavOutlet]="navDocumentList"></div>
  </div>

  <img
    class="cursor-pointer toggle-icon"
    *ngIf="!expanded"
    src="assets/images/icons/collab.svg"
    (click)="onToggle()"
    [ngbTooltip]="'Mở rộng danh sách'"
    container="body"
    aria-label="Mở"
  />
</div>

