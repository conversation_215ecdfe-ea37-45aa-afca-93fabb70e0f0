<ng-container *ngIf="isFullTableView; else graphView">
  <!-- Document Table Component (full view) -->
  <app-document-table
    [documentList]="documentList"
    [fullTableSearch]="fullTableSearch"
    [page]="page"
    [pageSize]="pageSize"
    [selectedTableRows]="selectedTableRows"
    [highlightedDocumentId]="highlightedDocumentId"
    [isTimKiemMode]="isTimKiemMode"
    (toggleView)="toggleFullDocumentTable()"
    (searchChange)="fullTableSearch = $event; onFullTableSearchChange()"
    (pageChange)="onPageChange($event)"
    (tableSelect)="onTableSelect($event)"
    (tableActivate)="onTableActivate($event)"
    (headerCheckboxChange)="onHeaderCheckboxChange($any({ target: { checked: $event } }))"
  ></app-document-table>
</ng-container>

<!-- Graph & list view -->
<ng-template #graphView>
  <ng-container *ngIf="!isGraphError; else graphError">
  <div *ngIf="!isLoadingGraphExternal; else graphLoading" class="graph-container">
  <!-- Graph Visualization Component -->
  <app-graph-visualization
    [nodes]="nodes"
    [links]="links"
    [apiNodeMap]="apiNodeMap"
    [rootNodeId]="rootNodeId"
    [isLoading]="isLoadingGraph || isLoadingGraphExternal"
    [d3Nodes]="d3Nodes"
    [d3Links]="d3Links"
    [callbacks]="graphCallbacks"
    [isTimKiemMode]="isTimKiemMode"
  ></app-graph-visualization>

  <!-- Document Details Panel Component -->
  <app-document-details-panel
    [visible]="showDocumentTable"
    [dataFile]="dataFile"
    [isClauseNode]="isClauseNode"
    [clauseContent]="clauseContent"
    [isLoadingClauseContent]="isLoadingClauseContent"
    [hasClauseContentError]="hasClauseContentError"
    [typeDocument]="typeDocument"
    [isTimKiemMode]="isTimKiemMode"
    (close)="onCloseDocumentTable()"
  ></app-document-details-panel>

  <!-- Document List Panel Component -->
  <app-document-list-panel
    [expanded]="documentListExpanded"
    [documentList]="documentList"
    [documentListSearch]="documentListSearch"
    [selectAllDocuments]="selectAllDocuments"
    [highlightedDocumentId]="highlightedDocumentId"
    [activeDocumentTab]="activeDocumentTab"
    [formState]="formState"
    [boLocMoiQuanHeOptions]="boLocMoiQuanHeOptions"
    [coQuanBanHanhOptions]="coQuanBanHanhOptions"
    [boLocLoaiVanBanOptions]="boLocLoaiVanBanOptions"
    [tinhTrangHieuLucOptions]="tinhTrangHieuLucOptions"
    [customYearOptions]="customYearOptions"
    (toggle)="toggleDocumentList()"
    (searchChange)="documentListSearch = $event; onDocumentListSearchChange()"
    (selectAllChange)="onSelectAllDocumentsChange($any({ target: { checked: $event } }))"
    (documentSelectChange)="onDocumentSelectChange($any({ target: { checked: $event.selected } }), $event.item)"
    (toggleFullTable)="toggleFullDocumentTable()"
    (viewModeChange)="onViewModeChange($event)"
    (moiQuanHeSelectionChange)="onMoiQuanHeSelectionChange($event)"
    (coQuanBanHanhSelectionChange)="onCoQuanBanHanhSelectionChange($event)"
    (loaiVanBanSelectionChange)="onLoaiVanBanSelectionChange($event)"
    (trangThaiHieuLucSelectionChange)="onTrangThaiHieuLucSelectionChange($event)"
    (yearChange)="changeYear($event)"
    (dateFilterModeChange)="setDateFilterMode($event.mode, $event.event)"
    (activeTabChange)="activeDocumentTab = $event"
    (documentHover)="onDocumentHover($event)"
  ></app-document-list-panel>

  <!-- Right Panel Component (only visible in TimKiem mode) -->
  <div *ngIf="isTimKiemMode" class="right-panel-container">
    <div class="right-panel" *ngIf="showRightPanel">
      <div class="right-panel-header d-flex align-items-center justify-content-between">
        <h4 class="mb-0 font-weight-bolder">{{ dataFile?.ten_day_du || '' }}</h4>
        <button
          type="button"
          class="btn btn-icon btn-sm ml-1"
          (click)="toggleRightPanel()"
          aria-label="Close"
        >
          <i data-feather="x"></i>
        </button>
      </div>
      <div class="right-panel-content">
        <span
          class="ref-title-show-detail-icon round d-inline-flex align-items-center justify-content-center mt-1 mb-1"
          (mouseenter)="showDocumentTooltip($event)"
          (mouseleave)="hideDocumentTooltip()"
        >
          <i data-feather="info" size="14"></i>
          <span class="ml-25">Chi tiết</span>
        </span>
        <div
          *ngIf="isClauseNode"
          class="clause-content-preview mb-1"
          [innerHTML]="getFormattedClauseContent()"
        ></div>
        <h5 class="mb-2 font-weight-bolder">VĂN BẢN LIÊN QUAN ({{ getRelatedDocumentsCount() }})</h5>
        <div class="related-documents-list" *ngIf="getRelatedDocumentsCount() > 0">
          <ng-container *ngFor="let group of getRelatedDocuments()">
            <div class="related-document-item" *ngFor="let doc of group.documents">
              <div class="relationship-label">
                <span class="relationship-type">{{ group.relationshipLabel }}</span>
                <span class="relationship-direction">({{ group.directionLabel }})</span>
              </div>
              <div class="document-info">
                <div class="document-title">{{ doc.ten_day_du }}</div>
                <div class="document-year">{{ doc.year }}</div>
              </div>
            </div>
          </ng-container>
        </div>
        <div class="no-related-documents" *ngIf="getRelatedDocumentsCount() === 0">
          <p class="text-muted mb-0">Không có văn bản liên quan</p>
        </div>
      </div>
    </div>
  </div>
</div>
</ng-container>

</ng-template>

<ng-template #graphLoading>
  <div class="graph-container graph-loading-overlay-wrapper">
    <div class="graph-loading-overlay">
      <div class="graph-loading-text">Đang cập nhật dữ liệu đồ thị...</div>
    </div>
  </div>
</ng-template>

<ng-template #graphError>
  <div class="graph-container graph-loading-overlay-wrapper">
    <div class="graph-loading-overlay">
      <div class="graph-loading-text">Dữ liệu đồ thị bị lỗi</div>
    </div>
  </div>
</ng-template>

<!-- Selection bar for saving selected documents (shows in both graph and table views) -->
<div class="save-file" *ngIf="selectedFiles.length > 0">
  <div
    class="selection-bar p-1 d-flex align-items-center justify-content-between"
  >
    <button
      class="close-btn mr-1"
      (click)="clearSelectedDocuments()"
      aria-label="Bỏ chọn tất cả"
    >
      &times;
    </button>
    <span class="height-30px mx-1"></span>
    <span class="selected-text"
      >Đã chọn {{ selectedFiles.length }} tài liệu</span
    >
    <span class="height-30px mx-1"></span>
    <button
      class="save-btn d-flex align-items-center"
      (click)="saveHistoryFiles()"
      [disabled]="isSavingFiles"
    >
      <ng-container *ngIf="!isSavingFiles; else savingSpinner">
        <img src="assets/images/icons/folder-star.svg" alt="folder-star" />
      </ng-container>
      <ng-template #savingSpinner>
        <span
          class="spinner-border spinner-border-sm text-primary"
          role="status"
        >
          <span class="sr-only">Loading...</span>
        </span>
      </ng-template>
      <span class="ms-2">{{ isSavingFiles ? 'Đang lưu' : 'Lưu tài liệu' }}</span>
    </button>
  </div>
</div>

<!-- Context Menu Component -->
<app-graph-context-menu
  [visible]="contextMenuVisible"
  [position]="contextMenuPosition"
  [menuItem]="contextMenuItem"
  [showExpandSubmenu]="showExpandSubmenu"
  (expandWithDefault)="onExpandNodeWithDefaultSchema()"
  (expandWithConditions)="openExpansionModal()"
  (restoreNode)="onRestoreNode()"
  (collapseNode)="onCollapseNode()"
  (toggleExpandSubmenu)="toggleExpandSubmenu($event)"
  (showSubmenu)="onShowSubmenu()"
  (hideSubmenu)="onHideSubmenu()"
  (close)="closeContextMenu()"
></app-graph-context-menu>

<!-- Expansion Modal Component -->
<app-expansion-modal
  [visible]="showExpansionModal"
  [formState]="modalFormState"
  [boLocMoiQuanHeOptions]="fullBoLocMoiQuanHeOptions"
  [coQuanBanHanhOptions]="fullCoQuanBanHanhOptions"
  [boLocLoaiVanBanOptions]="fullBoLocLoaiVanBanOptions"
  [tinhTrangHieuLucOptions]="fullTinhTrangHieuLucOptions"
  [customYearOptions]="customYearOptions"
  (close)="closeExpansionModal()"
  (submit)="onSubmitModalExpansion()"
  (yearChange)="changeYear($event, true)"
></app-expansion-modal>
