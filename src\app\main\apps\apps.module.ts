import { CommonModule, DatePipe } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { BrowserModule } from "@angular/platform-browser";
import { RouterModule } from "@angular/router";
import { CoreCommonModule } from "@core/common.module";
import { CoreSidebarModule } from "@core/components";
import { BulletPointPipe } from "@core/components/BulletPoint/bullet-point.pipe";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { TranslateModule } from "@ngx-translate/core";
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { AuthGuard } from "app/auth/helpers";
import { Role } from "app/auth/models";
import { ContentHeaderModule } from "app/layout/components/content-header/content-header.module";
import { PipeModule } from "app/layout/components/pipe/pipe.module";
import { FileUploadModule } from "ng2-file-upload";
import { Ng2FlatpickrModule } from "ng2-flatpickr";
import { MonacoEditorModule } from "ngx-monaco-editor";
import { PerfectScrollbarModule } from "ngx-perfect-scrollbar";

const routes = [
  {
    path: "quan-ly-van-ban",
    loadChildren: () =>
      import("./quan-ly-van-ban/quan-ly-van-ban.module").then(
        (m) => m.QuanLyVanBanModule
      ),
    data: {
      roles: [Role.User, Role.Admin, Role.Super_admin],
      cmsRoles: ['WRITER', 'REVIEWER', 'ADMIN']
    },
    canActivate: [AuthGuard],
  },
  {
    path: "cms",
    loadChildren: () =>
      import("./cms/cms.module").then(
        (m) => m.CmsModule
      ),
    data: {
      roles: [Role.User, Role.Admin, Role.Super_admin],
      cmsRoles: ['WRITER', 'REVIEWER', 'ADMIN']
    },

    canActivate: [AuthGuard],
  },
  {
    path: "super-admin",
    loadChildren: () =>
      import("./super-admin/super-admin.module").then(
        (m) => m.SuperAdminModule
      ),
    data: { roles: [Role.Admin, Role.Super_admin] },
    canActivate: [AuthGuard],
  },
  {
    path: "cau-hinh",
    loadChildren: () =>
      import("./cau-hinh/cau-hinh.module").then((m) => m.CauHinhModule),
    data: { roles: [Role.Super_admin] },
    canActivate: [AuthGuard],
  },
];

@NgModule({
  declarations: [BulletPointPipe],
  imports: [
    RouterModule.forChild(routes),
    ContentHeaderModule,
    CommonModule,
    PerfectScrollbarModule,
    NgxDatatableModule,
    ReactiveFormsModule,
    CoreSidebarModule,
    FormsModule,
    TranslateModule,
    CoreCommonModule,
    FileUploadModule,
    NgbModule,
    MonacoEditorModule,
    BrowserModule,
    PipeModule,
    Ng2FlatpickrModule,

    // NgxDiff2htmlModule,
  ],
  providers: [DatePipe],
  exports: [],
})
export class AppsModule { }
