import { Component, Input, Output, EventEmitter, ViewChild, AfterViewInit, OnChanges, SimpleChanges } from '@angular/core';
import { ColumnMode, SelectionType, DatatableComponent } from '@swimlane/ngx-datatable';
import { ApiNode } from '../../types/graph.types';
import { truncate } from '../../helper/helper';
import { DocumentListItem } from './document-list-panel.component';

@Component({
  selector: 'app-document-table',
  templateUrl: './document-table.component.html',
  styleUrls: ['./document-table.component.scss'],
})
export class DocumentTableComponent implements AfterViewInit, OnChanges {
  @ViewChild('fullTableDatatable') table!: DatatableComponent;
  
  @Input() documentList: DocumentListItem[] = [];
  @Input() fullTableSearch: string = '';
  @Input() page: number = 1;
  @Input() pageSize: number = 8;
  @Input() selectedTableRows: any[] = [];
  @Input() highlightedDocumentId: string | null = null;
  @Input() isTimKiemMode: boolean = false;

  @Output() toggleView = new EventEmitter<void>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() pageChange = new EventEmitter<number>();
  @Output() tableSelect = new EventEmitter<any>();
  @Output() tableActivate = new EventEmitter<any>();
  @Output() headerCheckboxChange = new EventEmitter<boolean>();

  ColumnMode = ColumnMode;
  SelectionType = SelectionType;

  getFilteredDocuments(): DocumentListItem[] {
    const query = (this.fullTableSearch || '').toLowerCase().trim();
    if (!query) {
      return this.documentList;
    }
    return this.documentList.filter((item) => {
      const attrs = item.apiNode?.thuoc_tinh || {};
      const title = (attrs.ten_day_du || item.title || '').toLowerCase();
      const loai = (attrs.loai_van_ban || '').toLowerCase();
      const soHieu = (attrs.so_hieu || '').toLowerCase();
      return title.includes(query) || loai.includes(query) || soHieu.includes(query);
    });
  }

  get totalItem(): number {
    return this.getFilteredDocuments().length;
  }

  getPaginatedDocuments(): any[] {
    const filtered = this.getFilteredDocuments();
    // Add properties for ngx-datatable sorting while maintaining object references
    const result = filtered.map((item: any) => {
      if ((item as any).loai_van_ban !== undefined) {
        return item;
      }
      return Object.assign(item, {
        loai_van_ban: item.apiNode?.thuoc_tinh?.loai_van_ban || '',
        so_hieu: item.apiNode?.thuoc_tinh?.so_hieu || '',
        co_quan_ban_hanh: item.apiNode?.thuoc_tinh?.co_quan_ban_hanh || '',
        ngay_ban_hanh: item.apiNode?.thuoc_tinh?.ngay_ban_hanh || '',
        ngay_co_hieu_luc: item.apiNode?.thuoc_tinh?.ngay_co_hieu_luc || '',
        tinh_trang_hieu_luc: item.apiNode?.thuoc_tinh?.tinh_trang_hieu_luc || '',
      });
    });
    const startIndex = (this.page - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    return result.slice(startIndex, endIndex);
  }

  getTruncatedDocumentName(item: any, maxChars: number = 50): string {
    const documentName = item.apiNode?.thuoc_tinh?.ten_day_du || item.title || '';
    return truncate(documentName, maxChars);
  }

  dateComparator = (propA: string, propB: string): number => {
    const dateA = propA ? new Date(propA).getTime() : 0;
    const dateB = propB ? new Date(propB).getTime() : 0;
    return dateA - dateB;
  };

  getRowClass = (row: any): string => {
    return row.id === this.highlightedDocumentId ? 'document-row--highlighted' : '';
  };

  isAllDocumentsSelected(): boolean {
    const filtered = this.getFilteredDocuments();
    if (filtered.length === 0) {
      return false;
    }
    return filtered.every((doc) => doc.selected);
  }

  onSearchChange(value: string): void {
    this.searchChange.emit(value);
  }

  onToggleView(): void {
    this.toggleView.emit();
  }

  onPageChange(event: any): void {
    this.pageChange.emit(event.offset + 1);
  }

  onTableSelect(event: any): void {
    this.tableSelect.emit(event);
  }

  onTableActivate(event: any): void {
    this.tableActivate.emit(event);
  }

  onHeaderCheckboxChange(event: any): void {
    this.headerCheckboxChange.emit(event.target.checked);
  }

  ngAfterViewInit(): void {
    // Recalculate table dimensions when in TimKiemMode
    if (this.isTimKiemMode && this.table) {
      setTimeout(() => {
        this.table.recalculate();
      }, 0);
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Recalculate when isTimKiemMode changes
    if (changes['isTimKiemMode'] && this.table && this.isTimKiemMode) {
      setTimeout(() => {
        this.table.recalculate();
      }, 0);
    }
  }
}

