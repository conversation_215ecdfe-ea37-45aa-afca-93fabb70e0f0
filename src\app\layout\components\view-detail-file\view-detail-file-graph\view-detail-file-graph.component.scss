.full-table-wrapper {
  overflow: hidden !important;
  flex: 1 1 auto;

  .ngx-datatable {
    width: max-content;
    min-width: 100%;
  }
}

.full-table-container {
  height: 100vh;
  display: block;

  &.tim-kiem-mode {
    padding: 5px;
  }

  .card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .card>.d-flex {
    flex-shrink: 0;
  }

  .full-table-wrapper {
    width: 100%;
  }
}

.graph-container {
  position: relative;

  .graph-content {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    height: auto;
    background: #fafafa;
    padding: 10px 10px 0 10px;
    transition: filter 0.2s ease;

    &.is-loading {
      filter: blur(3px);
      pointer-events: none;
    }

    .chart {
      width: 100%;
      height: 100%;
    }

    .loading-message {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #666;
      font-size: 16px;
    }
  }
}

.graph-loading-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.35);
  z-index: 2;
  font-weight: 500;
}

.simple-spinner {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-top-color: #ffffff;
  border-radius: 50%;
  animation: simple-spin 0.9s linear infinite;
}

@keyframes simple-spin {
  to {
    transform: rotate(360deg);
  }
}


// Graph node styles (neo4jd3-style multi-layer design)
.graph-node {

  // Ring layer (outer, transparent by default)
  .ring {
    fill: none;
    opacity: 0;
    stroke: #6ac6ff;
    stroke-width: 8px;
    pointer-events: none;
    transition: opacity 0.2s ease;
  }

  // Outline circle (main colored circle)
  .outline {
    cursor: pointer;
    pointer-events: all;
  }

  // Hover state - show ring (only for non-blurred nodes)
  &:hover:not(.node-blurred) .ring {
    opacity: 0.3;
  }

  // Selected state - show ring
  &.selected .ring {
    opacity: 0.3;
  }

  // Disable hover effects for blurred nodes
  &.node-blurred {

    // Keep pointer-events for cursor feedback, but hover won't trigger due to :not(.node-blurred) selector
    .ring {
      // Ensure ring doesn't show on hover for blurred nodes
      opacity: 0 !important;
    }
  }

  // Highlighted state - stronger ring
  &.node-highlighted .ring {
    opacity: 0.5;
    stroke: #888;
    stroke-width: 12px;
  }

  // Blurred node state
  .outline.graph-node--blurred {
    cursor: not-allowed;
  }
}

// Relationship/Edge styles (neo4jd3-style with overlay)
.relationship {
  cursor: default;

  // Outline path (visible relationship line)
  .outline {
    cursor: default;
    pointer-events: none; // Let overlay handle interactions
  }

  // Overlay path (wider, transparent, for hover detection only - no visual effect)
  .overlay {
    cursor: default;
    fill: none;
    stroke: transparent;
    stroke-width: 20px; // Wide stroke for easier hover detection
    opacity: 0;
    pointer-events: all; // Handle hover interactions
  }
}

// Relationship label text (with subtle background for readability)
.graph-link-labels {
  .graph-link-label {

    // Background rectangle (no stroke, subtle fill)
    .text-bg {
      stroke: none; // No outline/border
      pointer-events: none; // Don't interfere with interactions
    }

    // Text styling
    .text {
      cursor: default;
    }
  }
}

// Document table styles
.document-table-wrapper {
  position: absolute;
  bottom: 0;
  padding: 10px;
  width: 100%;
}

.document-table-container {
  height: auto;
  margin-top: 20px;
  background: white;

  .close-btn {
    appearance: none;
    border: none;
    background: transparent;
    color: #6c757d;
    font-size: 20px;
    line-height: 1;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 4px;

    &:hover {
      background: rgba(0, 0, 0, 0.06);
      color: #343a40;
    }

    &:focus {
      outline: 2px solid rgba(13, 110, 253, 0.4);
      outline-offset: 2px;
    }
  }
}

.document-table {
  width: 100%;
  border-collapse: collapse;

  .document-table__label {
    background-color: white;
    padding: 8px 12px;
    font-weight: 600;
    border: 1px solid #ddd;
    width: 20%;
  }

  .document-table__value {
    background-color: white;
    padding: 8px 12px;
    border: 1px solid #ddd;

    // Special styling for clause content cell
    &--clause {
      padding: 0;

      .clause-content-wrapper {
        max-height: 100px; // Testing with 100px
        overflow-y: auto;
        overflow-x: hidden;
        padding: 8px 12px;
        display: block;

        // Custom scrollbar styling
        &::-webkit-scrollbar {
          width: 8px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 4px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }
    }
  }
}

.document-status {
  padding: 8px 12px;

  &--active {
    background-color: rgb(220, 255, 220) !important;
    color: rgb(25, 135, 84);
    border: 1px solid rgb(200, 235, 200);
  }

  &--warning {
    background-color: rgb(255, 245, 200) !important;
    color: rgb(255, 165, 0);
    border: 1px solid rgb(255, 235, 180);
  }

  &--danger {
    background-color: rgb(255, 230, 230) !important;
    color: rgb(220, 53, 69);
    border: 1px solid rgb(255, 210, 210);
  }

  &--info {
    background-color: rgb(230, 245, 255) !important;
    color: rgb(13, 110, 253);
    border: 1px solid rgb(210, 235, 255);
  }
}

.graph-form-body {
  padding: 0 !important;
}

.graph-search-container {
  width: 300px;
}


// Document list styles
.document-list-container {
  --document-list-panel-width: 420px;
  position: absolute;
  left: 8px;
  top: 8px;
  z-index: 10;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  width: auto;

  .document-list-panel {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    width: 0;
    max-width: 100%;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transition: width 220ms ease, opacity 220ms ease, transform 220ms ease;
    opacity: 0;
    transform: translateX(-6px);

    .document-list-header {
      padding: 8px 10px 0 10px;
      border-bottom: 1px solid #eee;
      align-items: flex-start !important;
    }

    .close-btn {
      appearance: none;
      border: none;
      background: transparent;
      color: #6c757d;
      font-size: 18px;
      line-height: 1;
      cursor: pointer;
      padding: 2px 6px;
      border-radius: 4px;

      &:hover {
        background: rgba(0, 0, 0, 0.06);
        color: #343a40;
      }
    }

    .document-list-table-wrapper {
      // max-height: calc(80vh - 200px);
      overflow-y: auto;
      overflow-x: hidden;
    }

    .document-list-search-container {
      padding: 8px 10px;
    }

    .document-list-table {
      width: 100%;
      border-collapse: collapse;

      th,
      td {
        padding: 8px 10px;
        border-bottom: 1px solid #f1f1f1;
        font-size: 13px;
      }

      th {
        background: #fafafa;
        font-weight: 600;
        color: #333;
        position: sticky;
        top: 0;
        z-index: 1;
      }

      tr:last-child td {
        border-bottom: none;
      }
    }
  }

  .toggle-icon {
    width: 22px;
    height: 22px;
    margin-left: 8px;
  }

  &.expanded {
    width: var(--document-list-panel-width);
    max-width: var(--document-list-panel-width);

    .document-list-panel {
      width: 100%;
      max-width: 100%;
      opacity: 1;
      transform: translateX(0);
    }
  }
}

// Right panel styles (only visible in TimKiem mode)
.right-panel-container {
  --right-panel-width: 25rem;
  position: absolute;
  right: 8px;
  top: 8px;
  bottom: 8px;
  z-index: 10;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  width: auto;
  height: calc(100% - 16px);

  .right-panel {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    width: var(--right-panel-width);
    max-width: var(--right-panel-width);
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    opacity: 1;
    transform: translateX(0);

    .right-panel-header {
      padding: 8px 10px;
      border-bottom: 1px solid #eee;
      align-items: center !important;
      flex-shrink: 0;

      h4 {
        white-space: normal;
        word-break: break-word;
        line-height: 1.3;
      }
    }

    .right-panel-content {
      padding: 0 10px;
      overflow-y: auto;
      flex: 1 1 auto;
      min-height: 0;

      .clause-content-preview {
        display: block;
        font-size: 13px;
        line-height: 1.45;
        max-height: 35vh;
        overflow-y: auto;
        white-space: pre-wrap;
        margin-bottom: 10px;
      }

      h5 {
        font-size: 14px;
        margin-bottom: 12px;
      }

      .related-documents-list {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .related-document-item {
          border: 1px solid #e0e0e0;
          border-radius: 4px;
          padding: 8px 10px;
          background: #fafafa;
          transition: background-color 0.2s ease;

          &:hover {
            background: #f0f0f0;
          }

          .relationship-label {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-bottom: 6px;
            font-size: 12px;
            flex-wrap: wrap;

            .relationship-type {
              font-weight: 600;
            }

            .relationship-direction {
              color: #666;
              font-size: 11px;
            }
          }

          .document-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 8px;
            width: 100%;

            .document-title {
              flex: 1;
              font-size: 13px;
              line-height: 1.4;
              color: #333;
              word-break: break-word;
              white-space: normal;
            }

            .document-year {
              font-size: 12px;
              color: #666;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
}

.graph-loading-overlay-wrapper {
  position: relative;
  min-height: calc(100vh - 140px);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.graph-loading-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.92);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.graph-loading-text {
  font-weight: 600;
  color: #555;
  font-size: 15px;
}

.document-row--highlighted {
  background-color: rgba(40, 120, 240, 0.12) !important;

  td {
    background-color: transparent !important;
  }
}

// Remove selected row background in ngx-datatable
.datatable-body-row.active,
.datatable-body-row.selected {
  background-color: transparent !important;
}

.datatable-row-group {
  background-color: transparent !important;
}

// Fixed height for multiple-select ng-select containers
:host ::ng-deep {
  .fixed-height-select {
    .ng-select-container {
      height: 37.99px !important;
      min-height: 37.99px !important;
    }
  }
}

// Selection bar styles (similar to tim-kiem-thong-minh)
.th-checkbox {
  padding-left: 14px !important;
  padding-right: 14px !important;
}

.save-file {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  cursor: pointer;
  z-index: 9999;
}

.selection-bar {
  background: white;
  border-radius: 12px;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  padding: 12px 20px;
  min-width: 380px;
}

.selected-text {
  font-weight: 500;
  font-size: 16px;
  color: #333;
}

.height-30px {
  height: 30px;
  width: 1px;
  background: #e0e0e0;
}

.save-btn {
  background: none;
  border: none;
  color: #2878f0;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    opacity: 0.8;
  }

  img {
    width: 20px;
    height: 20px;
  }
}

.nav-documentlist-container {
  overflow-y: auto;
  max-height: 40vh;
}

:host ::ng-deep .table-header-title {
  display: inline-block;
  white-space: normal;
  line-height: 1.2;
  text-align: center;
}

// Context menu styles
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 180px;
  padding: 4px 0;

  ul {
    list-style: none;
    margin: 0;
    padding: 0;

    li {
      padding: 8px 16px;
      cursor: pointer;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #333;
      transition: background-color 0.2s;
      position: relative;

      &.disabled {
        color: #b0b0b0;
        cursor: not-allowed;
        pointer-events: none;
      }

      &:hover {
        background-color: #f5f5f5;
      }

      &.has-submenu {
        padding: 0;

        .menu-item-wrapper {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8px 8px 8px 16px;

          .menu-item-content {
            display: flex;
            align-items: center;
            flex: 1;
          }

          .submenu-trigger {
            width: 16px;
            height: 16px;
            margin: 0;
            padding: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 2px;
            transition: background-color 0.2s;

            &:hover {
              background-color: rgba(0, 0, 0, 0.08);
            }
          }
        }

        .submenu {
          position: absolute;
          left: 100%;
          top: 0;
          background: white;
          border: 1px solid #ddd;
          border-radius: 4px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          min-width: 180px;
          padding: 4px 0;
          margin-left: 4px;
          z-index: 1001;

          li {
            padding: 8px 16px;
            font-size: 14px;
            color: #333;

            &:hover {
              background-color: #f5f5f5;
            }

            span {
              width: auto;
              height: auto;
              margin: 0;
            }
          }
        }
      }

      span {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
    }
  }
}

// Expansion Modal styles
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1050;
  backdrop-filter: blur(2px);
}

.expansion-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  z-index: 1051;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;

  .modal-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .modal-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      appearance: none;
      border: none;
      background: transparent;
      color: #6c757d;
      font-size: 24px;
      line-height: 1;
      cursor: pointer;
      padding: 2px 8px;
      border-radius: 4px;

      &:hover {
        background: rgba(0, 0, 0, 0.06);
        color: #343a40;
      }

      &:focus {
        outline: 2px solid rgba(13, 110, 253, 0.4);
        outline-offset: 2px;
      }
    }
  }

  .modal-body {
    padding: 20px;
    overflow-y: auto;
    flex: 1;

    .form-group {
      margin-bottom: 16px;

      label {
        margin-bottom: 8px;
        font-size: 14px;
      }
    }
  }

  .modal-footer {
    padding: 12px 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}