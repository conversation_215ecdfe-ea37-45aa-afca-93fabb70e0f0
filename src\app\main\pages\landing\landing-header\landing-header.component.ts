import { Component, EventEmitter, HostListener, Input, Output, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-landing-header',
  templateUrl: './landing-header.component.html',
  styleUrls: ['./landing-header.component.scss'],
})
export class LandingHeaderComponent {
  // Giữ state active để tô đậm mục nav trên trang landing
  @Input() activeSection: 'chatbot'|'information'|'mission'|'articles'|'contact'|null = null;

  // Bật/tắt thanh nav (trang article-detail = false)
  @Input() showNav = true;

  // Link đăng nhập + logo/text để tái sử dụng
  // @Input() loginUrl: string | any[] = ['/pages/authentication/login-v2'];
  @Input() logoSrc: string = 'assets/images/logo/COpenAIlogo.svg';
  @Input() brandText = 'CLS';
  @Input() fullLoginUrl = '';
  @Output() sectionSelect = new EventEmitter<string>();
  // public fullLoginUrl = window.location.origin + '/pages/authentication/login-v2';

  isMenuOpen = false;
  isScrolled = false;
  private readonly LOGIN_PATH = '/pages/authentication/login-v2';
  private readonly AFTER_LOGIN_PATH = '/';
  isLoggedIn = false;
  constructor(private router: Router) {}
  ngOnInit(): void {
    this.checkLogin();
  }
    // ====== LOGIC KIỂM TRA ĐĂNG NHẬP ======
    private checkLogin(): void {
      const token = localStorage.getItem('token');
      this.isLoggedIn = !!token;
    }

  // ====== NÚT TRẢI NGHIỆM / ĐĂNG NHẬP ======
  onPrimaryClick(): void {
    if (this.isLoggedIn) {
      // đã đăng nhập → đi thẳng vào workspace/app
      this.router.navigateByUrl(this.AFTER_LOGIN_PATH);
    } else {
      // chưa đăng nhập → đi tới màn login
      if (this.fullLoginUrl) {
        // nếu login nằm ngoài SPA (full URL)
        window.location.href = this.fullLoginUrl;
      } else {
        // login là route trong Angular
        this.router.navigateByUrl(this.LOGIN_PATH);
      }
    }
  }

  // Đổi nền header khi scroll
  @HostListener('window:scroll')
  onWindowScroll(): void {
    this.isScrolled = window.scrollY > 4;
  }

  // Đóng menu khi lên desktop
  @HostListener('window:resize')
  onResize(): void {
    if (window.innerWidth > 992 && this.isMenuOpen) this.closeMenu();
  }

  toggleMenu(): void {
    this.isMenuOpen = !this.isMenuOpen;
    document.body.style.overflow = this.isMenuOpen ? 'hidden' : '';
  }

  closeMenu(): void {
    if (!this.isMenuOpen) return;
    this.isMenuOpen = false;
    document.body.style.overflow = '';
  }

  // Dùng khi có nav (landing)
  select(section: string) {
    this.sectionSelect.emit(section);
    this.closeMenu();
  }

  // Dùng khi không có nav (article-detail muốn về landing)
  gotoLanding() {
    this.router.navigate(['/home']);
    this.closeMenu();
  }

  // Dùng khi muốn mở login bằng header component
  gotoLogin() {
    this.closeMenu();
    this.router.navigate(['']);
  }
}
